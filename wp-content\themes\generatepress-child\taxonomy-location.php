<?php
/**
 * Location Taxonomy Template (City Pages) - Enhanced SEO Version
 */

get_header();

$current_term = get_queried_object();
$parent_term = null;
$is_city_page = false;

// Get parent term (state) if this is a city
if ($current_term->parent != 0) {
    $parent_term = get_term($current_term->parent, 'location');
    $is_city_page = true;
}

// Get city and state names for content
$city_name = $current_term->name;
$state_name = $parent_term ? $parent_term->name : '';
$location_display = $parent_term ? $city_name . ', ' . $state_name : $city_name;

// Get zoo count for this location
$zoo_count = $current_term->count;
?>

<div id="primary" class="content-area full-width-content">
    <main id="main" class="site-main">

        <!-- Enhanced Page Header - Zoo Style -->
        <div class="city-hero-section">
            <div class="hero-overlay"></div>
            <div class="hero-content-wrapper">
                <div class="hero-content">
                    <div class="breadcrumbs text-center">
                        <a href="<?php echo home_url(); ?>">Home</a>
                        <?php if ($parent_term) : ?>
                            <span class="separator">›</span>
                            <a href="<?php echo get_term_link($parent_term); ?>">
                                <?php echo esc_html($parent_term->name); ?>
                            </a>
                        <?php endif; ?>
                        <span class="separator">›</span>
                        <span class="current"><?php echo esc_html($current_term->name); ?></span>
                    </div>

                    <h1 class="city-title">
                        <?php if ($parent_term) : ?>
                            Best Petting Zoos in <?php echo esc_html($city_name); ?>, <?php echo esc_html($state_name); ?>
                        <?php else : ?>
                            Petting Zoos in <?php echo esc_html($current_term->name); ?>
                        <?php endif; ?>
                    </h1>

                    <?php if ($is_city_page) : ?>
                        <p class="hero-subtitle text-center">
                            Discover <?php echo $zoo_count; ?> amazing petting zoo<?php echo $zoo_count !== 1 ? 's' : ''; ?> perfect for family adventures
                        </p>
                    <?php else : ?>
                        <p class="hero-subtitle text-center">
                            Explore <?php echo $zoo_count; ?> petting zoos across <?php echo esc_html($current_term->name); ?>
                        </p>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- Description Section (moved outside hero) -->
        <div class="container" style="max-width: 1400px; margin: 0 auto; padding: 0rem 2rem 0;">
            <div class="city-intro-content text-center" style="max-width: 800px; margin: 0 auto;">
                <?php if ($current_term->description) : ?>
                    <?php echo wpautop($current_term->description); ?>
                <?php else : ?>
                    <?php if ($is_city_page) : ?>
                        <p>We've carefully selected the top-rated petting zoos in <?php echo esc_html($location_display); ?> based on family reviews, safety standards, and the quality of animal experiences. Each location offers unique features and activities perfect for creating memorable family adventures.</p>
                    <?php else : ?>
                        <p>Discover the best petting zoos and animal farms throughout <?php echo esc_html($current_term->name); ?>.
                        With <?php echo $zoo_count; ?> locations across the state, families have plenty of options for interactive animal experiences,
                        educational programs, and outdoor fun that creates lasting memories.</p>
                    <?php endif; ?>
                <?php endif; ?>
            </div>
        </div>

        <?php if ($is_city_page) : ?>
            <!-- New SEO-Focused H2 Sections -->
            <div class="container" style="max-width: 1400px; margin: 0 auto; padding: 0 2rem;">

                <?php
                // Function to generate zoo cards for sections
                function generate_zoo_cards_section($zoos_query, $section_id = '') {
                    if ($zoos_query->have_posts()) : ?>
                        <div class="petting-zoo-cards-grid" <?php echo $section_id ? 'id="' . esc_attr($section_id) . '"' : ''; ?>>
                            <?php while ($zoos_query->have_posts()) : $zoos_query->the_post();
                                // Get the first picture from JSON data
                                $json_data = get_post_meta(get_the_ID(), '_petting_zoo_json_data', true);
                                $pics = array();
                                if ($json_data) {
                                    $data = json_decode($json_data, true);
                                    if (isset($data['pics']) && is_array($data['pics'])) {
                                        $pics = $data['pics'];
                                    }
                                }

                                $first_pic = '';
                                if (!empty($pics) && isset($pics[0]['url'])) {
                                    $first_pic = $pics[0]['url'];
                                } else {
                                    // Fallback to placeholder
                                    $first_pic = '/wp-content/uploads/2025/06/pettingzoophoto (1).webp';
                                }

                                // Get the first 10 words from the post content for description
                                $post_content = get_the_content();
                                $post_content = wp_strip_all_tags($post_content);
                                $first_10_words = wp_trim_words($post_content, 10, '...');
                            ?>
                                <div class="petting-zoo-card">
                                    <div class="card-image">
                                        <a href="<?php the_permalink(); ?>">
                                            <img src="<?php echo esc_url($first_pic); ?>" alt="<?php the_title(); ?>" loading="lazy">
                                        </a>
                                        <?php
                                        // Get rating if available
                                        $rating = get_post_meta(get_the_ID(), '_petting_zoo_rating', true);
                                        if ($rating) : ?>
                                            <div class="rating-badge">
                                                <span class="rating-number"><?php echo esc_html($rating); ?></span>
                                                <span class="rating-star">★</span>
                                            </div>
                                        <?php endif; ?>
                                    </div>

                                    <div class="card-content">
                                        <h3 class="zoo-name">
                                            <a href="<?php the_permalink(); ?>"><?php the_title(); ?></a>
                                        </h3>

                                        <?php
                                        $address = get_post_meta(get_the_ID(), '_petting_zoo_address', true);
                                        if ($address) : ?>
                                            <div class="zoo-location">
                                                <span class="location-icon">📍</span>
                                                <?php echo esc_html($address); ?>
                                            </div>
                                        <?php endif; ?>

                                        <div class="activities-section">
                                            <?php
                                            $zoo_features = get_the_terms(get_the_ID(), 'features');
                                            if ($zoo_features && !is_wp_error($zoo_features)) : ?>
                                                <div class="activity-tags">
                                                    <?php
                                                    $feature_count = 0;
                                                    foreach ($zoo_features as $feature) :
                                                        if ($feature_count >= 4) break; // Show up to 4 features
                                                        ?>
                                                        <span class="activity-tag"><?php echo esc_html($feature->name); ?></span>
                                                        <?php
                                                        $feature_count++;
                                                    endforeach;

                                                    if (count($zoo_features) > 4) : ?>
                                                        <span class="activity-tag more-tag">+<?php echo count($zoo_features) - 4; ?> more</span>
                                                    <?php endif; ?>
                                                </div>
                                            <?php else : ?>
                                                <div class="activity-tags">
                                                    <span class="activity-tag">Animal Feeding</span>
                                                    <span class="activity-tag">Educational Tours</span>
                                                    <span class="activity-tag">Family Fun</span>
                                                </div>
                                            <?php endif; ?>
                                        </div>

                                        <div class="card-description">
                                            <?php echo esc_html($first_10_words); ?>
                                        </div>

                                        <a href="<?php the_permalink(); ?>" class="view-details-btn">
                                            View Details <span class="arrow">→</span>
                                        </a>
                                    </div>
                                </div>
                            <?php endwhile; ?>
                        </div>
                    <?php endif;
                    wp_reset_postdata();
                }

                // 1. Best Petting Zoos by Ranking
                $ranking_args = array(
                    'post_type' => 'petting_zoo',
                    'posts_per_page' => 3,
                    'tax_query' => array(
                        array(
                            'taxonomy' => 'location',
                            'field' => 'term_id',
                            'terms' => $current_term->term_id,
                        )
                    ),
                    'meta_key' => '_petting_zoo_rating',
                    'orderby' => 'meta_value_num',
                    'order' => 'DESC'
                );
                $ranking_query = new WP_Query($ranking_args);

                if ($ranking_query->have_posts()) : ?>
                    <section class="city-ranking-section">
                        <h2>Best Petting Zoos in <?php echo esc_html($city_name); ?> by Ranking</h2>
                        <p>Discover the highest-rated petting zoos in <?php echo esc_html($location_display); ?> based on visitor reviews and family experiences. These top-ranked destinations consistently deliver exceptional animal encounters and memorable family adventures.</p>
                        <?php generate_zoo_cards_section($ranking_query, 'ranking-zoos'); ?>
                    </section>
                <?php endif; ?>

                <?php
                // 2. Best Petting Zoos for Birthday Parties
                $party_args = array(
                    'post_type' => 'petting_zoo',
                    'posts_per_page' => 3,
                    'tax_query' => array(
                        'relation' => 'AND',
                        array(
                            'taxonomy' => 'location',
                            'field' => 'term_id',
                            'terms' => $current_term->term_id,
                        ),
                        array(
                            'relation' => 'OR',
                            array(
                                'taxonomy' => 'event_type',
                                'field' => 'slug',
                                'terms' => array('birthday-parties', 'birthday-party', 'parties'),
                                'operator' => 'IN'
                            ),
                            array(
                                'taxonomy' => 'features',
                                'field' => 'slug',
                                'terms' => array('birthday-parties', 'party-packages', 'private-events'),
                                'operator' => 'IN'
                            )
                        )
                    )
                );
                $party_query = new WP_Query($party_args);

                if ($party_query->have_posts()) : ?>
                    <section class="city-birthday-section">
                        <h2>Best Petting Zoos for Birthday Parties in <?php echo esc_html($city_name); ?></h2>
                        <p>Make your child's birthday unforgettable with these <?php echo esc_html($city_name); ?> petting zoos that specialize in birthday celebrations. From party packages to private animal encounters, these venues create magical memories for the special day.</p>
                        <?php generate_zoo_cards_section($party_query, 'birthday-zoos'); ?>
                    </section>
                <?php endif; ?>

                <?php
                // 3. Best Mobile Petting Zoo
                $mobile_args = array(
                    'post_type' => 'petting_zoo',
                    'posts_per_page' => 3,
                    'tax_query' => array(
                        'relation' => 'AND',
                        array(
                            'taxonomy' => 'location',
                            'field' => 'term_id',
                            'terms' => $current_term->term_id,
                        ),
                        array(
                            'taxonomy' => 'zoo_type',
                            'field' => 'slug',
                            'terms' => array('mobile', 'mobile-petting-zoo', 'traveling'),
                            'operator' => 'IN'
                        )
                    )
                );
                $mobile_query = new WP_Query($mobile_args);

                if ($mobile_query->have_posts()) : ?>
                    <section class="city-mobile-section">
                        <h2>Best Mobile Petting Zoo in <?php echo esc_html($city_name); ?></h2>
                        <p>Bring the petting zoo experience directly to your location with these mobile services in <?php echo esc_html($location_display); ?>. Perfect for backyard parties, school events, and community gatherings where the animals come to you.</p>
                        <?php generate_zoo_cards_section($mobile_query, 'mobile-zoos'); ?>
                    </section>
                <?php endif; ?>

                <?php
                // 4. Best Petting Zoos with Bunnies
                $bunny_args = array(
                    'post_type' => 'petting_zoo',
                    'posts_per_page' => 3,
                    'tax_query' => array(
                        'relation' => 'AND',
                        array(
                            'taxonomy' => 'location',
                            'field' => 'term_id',
                            'terms' => $current_term->term_id,
                        ),
                        array(
                            'taxonomy' => 'animal_type',
                            'field' => 'slug',
                            'terms' => array('bunnies', 'rabbits', 'bunny', 'rabbit'),
                            'operator' => 'IN'
                        )
                    )
                );
                $bunny_query = new WP_Query($bunny_args);

                if ($bunny_query->have_posts()) : ?>
                    <section class="city-bunny-section">
                        <h2>Best Petting Zoos in <?php echo esc_html($city_name); ?> with Bunnies</h2>
                        <p>Experience the joy of cuddling with adorable bunnies at these <?php echo esc_html($location_display); ?> petting zoos. These gentle, soft creatures are perfect for young children and create heartwarming moments that families treasure forever.</p>
                        <?php generate_zoo_cards_section($bunny_query, 'bunny-zoos'); ?>
                    </section>
                <?php endif; ?>

                <?php
                // 5. Best Indoor Petting Zoo
                $indoor_args = array(
                    'post_type' => 'petting_zoo',
                    'posts_per_page' => 3,
                    'tax_query' => array(
                        'relation' => 'AND',
                        array(
                            'taxonomy' => 'location',
                            'field' => 'term_id',
                            'terms' => $current_term->term_id,
                        ),
                        array(
                            'relation' => 'OR',
                            array(
                                'taxonomy' => 'zoo_type',
                                'field' => 'slug',
                                'terms' => array('indoor', 'indoor-facility', 'climate-controlled'),
                                'operator' => 'IN'
                            ),
                            array(
                                'taxonomy' => 'features',
                                'field' => 'slug',
                                'terms' => array('indoor', 'indoor-activities', 'climate-controlled', 'weather-proof'),
                                'operator' => 'IN'
                            )
                        )
                    )
                );
                $indoor_query = new WP_Query($indoor_args);

                if ($indoor_query->have_posts()) : ?>
                    <section class="city-indoor-section">
                        <h2>Best Indoor Petting Zoo in <?php echo esc_html($city_name); ?></h2>
                        <p>Enjoy year-round animal encounters regardless of weather at these indoor petting zoos in <?php echo esc_html($location_display); ?>. Perfect for rainy days, hot summers, or cold winters when you want guaranteed family fun in a comfortable environment.</p>
                        <?php generate_zoo_cards_section($indoor_query, 'indoor-zoos'); ?>
                    </section>
                <?php endif; ?>

            </div>
        <?php endif; ?>

        <div class="container" style="max-width: 1400px; margin: 0 auto; padding: 0 2rem;"">

            <?php if ($is_city_page) : ?>
                <!-- H2: Best Petting Zoos in City -->
                <section class="city-zoos-section">
                    <h2>Best Petting Zoos in <?php echo esc_html($city_name); ?></h2>
                    <p>We've carefully selected the top-rated petting zoos in <?php echo esc_html($location_display); ?> based on family reviews,
                    safety standards, and the quality of animal experiences. Each location offers unique features and activities perfect for creating
                    memorable family adventures.</p>

                    <!-- Enhanced Filters Section -->
                    <div class="zoo-filters-section">
                        <h3>🔍 Find Your Perfect Petting Zoo Experience</h3>
                        <div class="zoo-filters">
                            <select name="animal_type" onchange="applyFilters()" aria-label="Filter by animal type">
                                <option value="">🐐 All Animal Types</option>
                                <?php
                                $animals = get_terms(array(
                                    'taxonomy' => 'animal_type',
                                    'hide_empty' => true
                                ));

                                $selected_animal = isset($_GET['animal_type']) ? $_GET['animal_type'] : '';

                                foreach ($animals as $animal) {
                                    $selected = ($selected_animal === $animal->slug) ? 'selected' : '';
                                    echo '<option value="' . esc_attr($animal->slug) . '" ' . $selected . '>' . esc_html($animal->name) . '</option>';
                                }
                                ?>
                            </select>

                            <select name="zoo_type" onchange="applyFilters()" aria-label="Filter by zoo type">
                                <option value="">🏡 All Zoo Types</option>
                                <?php
                                $zoo_types = get_terms(array(
                                    'taxonomy' => 'zoo_type',
                                    'hide_empty' => true
                                ));

                                $selected_type = isset($_GET['zoo_type']) ? $_GET['zoo_type'] : '';

                                foreach ($zoo_types as $type) {
                                    $selected = ($selected_type === $type->slug) ? 'selected' : '';
                                    echo '<option value="' . esc_attr($type->slug) . '" ' . $selected . '>' . esc_html($type->name) . '</option>';
                                }
                                ?>
                            </select>

                            <select name="features" onchange="applyFilters()" aria-label="Filter by features">
                                <option value="">⭐ All Features</option>
                                <?php
                                $features = get_terms(array(
                                    'taxonomy' => 'features',
                                    'hide_empty' => true
                                ));

                                $selected_feature = isset($_GET['features']) ? $_GET['features'] : '';

                                foreach ($features as $feature) {
                                    $selected = ($selected_feature === $feature->slug) ? 'selected' : '';
                                    echo '<option value="' . esc_attr($feature->slug) . '" ' . $selected . '>' . esc_html($feature->name) . '</option>';
                                }
                                ?>
                            </select>
                        </div>
                    </div>
                </section>
            <?php else : ?>
                <!-- State page filters -->
                <div class="zoo-filters-section">
                    <h3>Filter Results</h3>
                    <div class="zoo-filters">
                        <select name="animal_type" onchange="applyFilters()">
                            <option value="">All Animal Types</option>
                            <?php
                            $animals = get_terms(array(
                                'taxonomy' => 'animal_type',
                                'hide_empty' => true
                            ));

                            $selected_animal = isset($_GET['animal_type']) ? $_GET['animal_type'] : '';

                            foreach ($animals as $animal) {
                                $selected = ($selected_animal === $animal->slug) ? 'selected' : '';
                                echo '<option value="' . esc_attr($animal->slug) . '" ' . $selected . '>' . esc_html($animal->name) . '</option>';
                            }
                            ?>
                        </select>

                        <select name="zoo_type" onchange="applyFilters()">
                            <option value="">All Zoo Types</option>
                            <?php
                            $zoo_types = get_terms(array(
                                'taxonomy' => 'zoo_type',
                                'hide_empty' => true
                            ));

                            $selected_type = isset($_GET['zoo_type']) ? $_GET['zoo_type'] : '';

                            foreach ($zoo_types as $type) {
                                $selected = ($selected_type === $type->slug) ? 'selected' : '';
                                echo '<option value="' . esc_attr($type->slug) . '" ' . $selected . '>' . esc_html($type->name) . '</option>';
                            }
                            ?>
                        </select>

                        <select name="features" onchange="applyFilters()">
                            <option value="">All Features</option>
                            <?php
                            $features = get_terms(array(
                                'taxonomy' => 'features',
                                'hide_empty' => true
                            ));

                            $selected_feature = isset($_GET['features']) ? $_GET['features'] : '';

                            foreach ($features as $feature) {
                                $selected = ($selected_feature === $feature->slug) ? 'selected' : '';
                                echo '<option value="' . esc_attr($feature->slug) . '" ' . $selected . '>' . esc_html($feature->name) . '</option>';
                            }
                            ?>
                        </select>
                    </div>
                </div>
            <?php endif; ?>

            <!-- Results Count -->
            <?php
            $paged = (get_query_var('paged')) ? get_query_var('paged') : 1;

            // Build query args
            $args = array(
                'post_type' => 'petting_zoo',
                'posts_per_page' => 12,
                'paged' => $paged,
                'tax_query' => array(
                    array(
                        'taxonomy' => 'location',
                        'field' => 'term_id',
                        'terms' => $current_term->term_id,
                    )
                )
            );

            // Add additional filters
            $tax_query = array('relation' => 'AND');
            $tax_query[] = array(
                'taxonomy' => 'location',
                'field' => 'term_id',
                'terms' => $current_term->term_id,
            );

            if (isset($_GET['animal_type']) && !empty($_GET['animal_type'])) {
                $tax_query[] = array(
                    'taxonomy' => 'animal_type',
                    'field' => 'slug',
                    'terms' => sanitize_text_field($_GET['animal_type']),
                );
            }

            if (isset($_GET['zoo_type']) && !empty($_GET['zoo_type'])) {
                $tax_query[] = array(
                    'taxonomy' => 'zoo_type',
                    'field' => 'slug',
                    'terms' => sanitize_text_field($_GET['zoo_type']),
                );
            }

            if (isset($_GET['features']) && !empty($_GET['features'])) {
                $tax_query[] = array(
                    'taxonomy' => 'features',
                    'field' => 'slug',
                    'terms' => sanitize_text_field($_GET['features']),
                );
            }

            $args['tax_query'] = $tax_query;

            $zoo_query = new WP_Query($args);
            ?>

            <div class="results-info">
                <p>Found <?php echo $zoo_query->found_posts; ?> petting zoo<?php echo $zoo_query->found_posts !== 1 ? 's' : ''; ?>
                in <?php echo esc_html($current_term->name); ?></p>
            </div>

            <!-- Enhanced Petting Zoos Grid - 3 Column Layout -->
            <?php if ($zoo_query->have_posts()) : ?>
                <div class="petting-zoo-cards-grid">
                    <?php while ($zoo_query->have_posts()) : $zoo_query->the_post();
                        // Get the first picture from JSON data
                        $json_data = get_post_meta(get_the_ID(), '_petting_zoo_json_data', true);
                        $pics = array();
                        if ($json_data) {
                            $data = json_decode($json_data, true);
                            if (isset($data['pics']) && is_array($data['pics'])) {
                                $pics = $data['pics'];
                            }
                        }

                        $first_pic = '';
                        if (!empty($pics) && isset($pics[0]['url'])) {
                            $first_pic = $pics[0]['url'];
                        } else {
                            // Fallback to placeholder
                            $first_pic = '/wp-content/uploads/2025/06/pettingzoophoto (1).webp';
                        }

                        // Get the first 10 words from the post content for description
                        $post_content = get_the_content();
                        $post_content = wp_strip_all_tags($post_content);
                        $first_10_words = wp_trim_words($post_content, 10, '...');
                    ?>
                        <div class="petting-zoo-card">
                            <div class="card-image">
                                <a href="<?php the_permalink(); ?>">
                                    <img src="<?php echo esc_url($first_pic); ?>" alt="<?php the_title(); ?>" loading="lazy">
                                </a>
                                <?php
                                // Get rating if available
                                $rating = get_post_meta(get_the_ID(), '_petting_zoo_rating', true);
                                if ($rating) : ?>
                                    <div class="rating-badge">
                                        <span class="rating-number"><?php echo esc_html($rating); ?></span>
                                        <span class="rating-star">★</span>
                                    </div>
                                <?php endif; ?>
                            </div>

                            <div class="card-content">
                                <h3 class="zoo-name">
                                    <a href="<?php the_permalink(); ?>"><?php the_title(); ?></a>
                                </h3>

                                <?php
                                $address = get_post_meta(get_the_ID(), '_petting_zoo_address', true);
                                if ($address) : ?>
                                    <div class="zoo-location">
                                        <span class="location-icon">📍</span>
                                        <?php echo esc_html($address); ?>
                                    </div>
                                <?php endif; ?>

                                <div class="activities-section">
                                    <?php
                                    $zoo_features = get_the_terms(get_the_ID(), 'features');
                                    if ($zoo_features && !is_wp_error($zoo_features)) : ?>
                                        <div class="activity-tags">
                                            <?php
                                            $feature_count = 0;
                                            foreach ($zoo_features as $feature) :
                                                if ($feature_count >= 4) break; // Show up to 4 features
                                                ?>
                                                <span class="activity-tag"><?php echo esc_html($feature->name); ?></span>
                                                <?php
                                                $feature_count++;
                                            endforeach;

                                            if (count($zoo_features) > 4) : ?>
                                                <span class="activity-tag more-tag">+<?php echo count($zoo_features) - 4; ?> more</span>
                                            <?php endif; ?>
                                        </div>
                                    <?php else : ?>
                                        <div class="activity-tags">
                                            <span class="activity-tag">Animal Feeding</span>
                                            <span class="activity-tag">Educational Tours</span>
                                            <span class="activity-tag">Family Fun</span>
                                        </div>
                                    <?php endif; ?>
                                </div>

                                <div class="card-description">
                                    <?php echo esc_html($first_10_words); ?>
                                </div>

                                <a href="<?php the_permalink(); ?>" class="view-details-btn">
                                    View Details <span class="arrow">→</span>
                                </a>
                            </div>
                        </div>
                    <?php endwhile; ?>
                </div>

                <!-- Pagination -->
                <?php
                $pagination = paginate_links(array(
                    'total' => $zoo_query->max_num_pages,
                    'current' => $paged,
                    'format' => '?paged=%#%',
                    'prev_text' => '← Previous',
                    'next_text' => 'Next →',
                ));

                if ($pagination) : ?>
                    <div class="pagination-wrapper">
                        <?php echo $pagination; ?>
                    </div>
                <?php endif; ?>

            <?php else : ?>
                <div class="no-results">
                    <h3>No petting zoos found</h3>
                    <p>Sorry, we couldn't find any petting zoos matching your criteria in <?php echo esc_html($current_term->name); ?>.</p>
                    <a href="<?php echo home_url(); ?>" class="btn">Browse All Locations</a>
                </div>
            <?php endif; ?>

            <?php wp_reset_postdata(); ?>

            <?php if ($is_city_page && $zoo_query->have_posts()) : ?>
                <!-- H2: What Makes Petting Zoos in [City] Great for Families? -->
                <section class="city-family-benefits">
                    <h2>What Makes Petting Zoos in <?php echo esc_html($city_name); ?> Great for Families?</h2>
                    <p>Petting zoos in <?php echo esc_html($location_display); ?> offer exceptional family experiences that combine education,
                    entertainment, and hands-on learning. These carefully maintained facilities provide safe environments where children can
                    interact with gentle animals while learning about animal care, agriculture, and nature.</p>

                    <div class="family-benefits-grid">
                        <div class="benefit-item">
                            <h4>🛡️ Safe & Clean Environments</h4>
                            <p>All petting zoos in <?php echo esc_html($city_name); ?> maintain high safety and cleanliness standards,
                            with regular health checks for animals and sanitization stations for visitors.</p>
                        </div>
                        <div class="benefit-item">
                            <h4>👨‍👩‍👧‍👦 Perfect for All Ages</h4>
                            <p>From toddlers to grandparents, these petting zoo experiences in <?php echo esc_html($city_name); ?>
                            are designed to engage visitors of all ages with age-appropriate activities.</p>
                        </div>
                        <div class="benefit-item">
                            <h4>🎓 Educational Value</h4>
                            <p>Children learn about animal behavior, responsibility, and empathy while having fun feeding and
                            petting friendly farm animals and exotic species.</p>
                        </div>
                        <div class="benefit-item">
                            <h4>💰 Affordable Family Fun</h4>
                            <p>Petting zoos in <?php echo esc_html($location_display); ?> offer excellent value for families,
                            with reasonable admission prices and often free parking.</p>
                        </div>
                    </div>
                </section>
            <?php endif; ?>

            <?php if ($is_city_page && $zoo_query->have_posts()) : ?>
                <!-- H2: Top Features Available in [City]'s Petting Zoos -->
                <section class="city-features-section">
                    <h2>Top Features Available in <?php echo esc_html($city_name); ?>'s Petting Zoos</h2>
                    <p>The petting zoos in <?php echo esc_html($location_display); ?> offer a wide variety of features and amenities
                    to enhance your family's visit. From interactive animal feeding to educational programs, here's what you can expect:</p>

                    <?php
                    // Get all features used by zoos in this city
                    $city_features = get_terms(array(
                        'taxonomy' => 'features',
                        'hide_empty' => true,
                        'object_ids' => wp_list_pluck($zoo_query->posts, 'ID')
                    ));

                    if ($city_features && !is_wp_error($city_features)) : ?>
                        <div class="features-grid">
                            <?php foreach (array_slice($city_features, 0, 6) as $feature) :
                                // Get zoos that have this feature
                                $zoos_with_feature = get_posts(array(
                                    'post_type' => 'petting_zoo',
                                    'posts_per_page' => -1,
                                    'tax_query' => array(
                                        'relation' => 'AND',
                                        array(
                                            'taxonomy' => 'location',
                                            'field' => 'term_id',
                                            'terms' => $current_term->term_id,
                                        ),
                                        array(
                                            'taxonomy' => 'features',
                                            'field' => 'term_id',
                                            'terms' => $feature->term_id,
                                        )
                                    )
                                ));
                                ?>
                                <div class="feature-item">
                                    <h4><a href="<?php echo get_term_link($feature); ?>">✨ <?php echo esc_html($feature->name); ?></a></h4>
                                    <?php if (!empty($zoos_with_feature)) : ?>
                                        <div class="feature-zoos">
                                            <?php
                                            $zoo_names = array();
                                            foreach ($zoos_with_feature as $zoo) {
                                                $zoo_names[] = '<a href="' . get_permalink($zoo->ID) . '">' . esc_html($zoo->post_title) . '</a>';
                                            }
                                            if (count($zoo_names) == 1) {
                                                echo 'Available at ' . $zoo_names[0];
                                            } elseif (count($zoo_names) == 2) {
                                                echo 'Available at ' . implode(' and ', $zoo_names);
                                            } else {
                                                $last_zoo = array_pop($zoo_names);
                                                echo 'Available at ' . implode(', ', $zoo_names) . ', and ' . $last_zoo;
                                            }
                                            ?>
                                        </div>
                                    <?php endif; ?>
                                </div>
                            <?php endforeach; ?>
                        </div>

                        <div class="features-cta">
                            <p><strong>Looking for specific features?</strong> Use our filters above to find petting zoos in
                            <?php echo esc_html($city_name); ?> with exactly what you're looking for!</p>
                        </div>
                    <?php endif; ?>
                </section>

                <!-- H2: Tips for Visiting a Petting Zoo in [City] -->
                <section class="city-tips-section">
                    <h2>Tips for Visiting a Petting Zoo in <?php echo esc_html($city_name); ?></h2>
                    <p>Make the most of your petting zoo adventure in <?php echo esc_html($location_display); ?> with these helpful tips
                    from local families and zoo experts:</p>

                    <div class="tips-grid">
                        <div class="tip-category">
                            <h4>🚗 Parking & Arrival</h4>
                            <ul>
                                <li>Most <?php echo esc_html($city_name); ?> petting zoos offer free parking</li>
                                <li>Arrive early (especially weekends) for the best experience</li>
                                <li>Check individual zoo websites for specific parking instructions</li>
                            </ul>
                        </div>

                        <div class="tip-category">
                            <h4>🎒 What to Bring</h4>
                            <ul>
                                <li>Hand sanitizer (though most locations provide it)</li>
                                <li>Comfortable walking shoes for the whole family</li>
                                <li>Camera for capturing special moments</li>
                                <li>Weather-appropriate clothing for outdoor activities</li>
                            </ul>
                        </div>

                        <div class="tip-category">
                            <h4>⏰ Best Times to Visit</h4>
                            <ul>
                                <li>Weekday mornings are typically less crowded</li>
                                <li>Animals are most active during cooler parts of the day</li>
                                <li>Check feeding schedules for interactive experiences</li>
                            </ul>
                        </div>

                        <div class="tip-category">
                            <h4>👶 With Young Children</h4>
                            <ul>
                                <li>Bring strollers - most <?php echo esc_html($city_name); ?> zoos are stroller-friendly</li>
                                <li>Pack snacks and water bottles</li>
                                <li>Plan for 2-3 hours for a full experience</li>
                            </ul>
                        </div>
                    </div>
                </section>
            <?php endif; ?>

            <!-- Child Locations (if this is a state page) -->
            <?php if ($current_term->parent == 0) : // This is a state page ?>
                <?php
                $child_locations = get_terms(array(
                    'taxonomy' => 'location',
                    'hide_empty' => true,
                    'parent' => $current_term->term_id,
                    'orderby' => 'count',
                    'order' => 'DESC'
                ));

                if (!empty($child_locations)) : ?>
                    <section class="child-locations section">
                        <h2>Cities in <?php echo esc_html($current_term->name); ?></h2>
                        <div class="cities-grid">
                            <?php foreach ($child_locations as $city) : ?>
                                <a href="<?php echo get_term_link($city); ?>" class="city-card">
                                    <h4><?php echo esc_html($city->name); ?></h4>
                                    <div class="zoo-count"><?php echo $city->count; ?> Petting Zoo<?php echo $city->count !== 1 ? 's' : ''; ?></div>
                                </a>
                            <?php endforeach; ?>
                        </div>
                    </section>
                <?php endif; ?>
            <?php endif; ?>

            <?php if ($is_city_page && $zoo_query->have_posts()) : ?>
                <!-- H2: Why Families (Especially Dads) Love Petting Zoos in [City] -->
                <section class="dad-focused-section">
                    <h2>Why Families (Especially Dads) Love Petting Zoos in <?php echo esc_html($city_name); ?></h2>
                    <p>Petting zoos in <?php echo esc_html($location_display); ?> offer the perfect blend of outdoor adventure,
                    educational value, and screen-free family time that dads appreciate. Here's why these experiences are becoming
                    the go-to weekend activity for fathers and their children:</p>

                    <div class="dad-benefits-grid">
                        <div class="dad-benefit">
                            <h4>🏃‍♂️ Active, Engaging Experience</h4>
                            <p>No sitting still required! Petting zoos provide hands-on activities that keep kids engaged and
                            moving, making it easy for dads to participate and bond with their children.</p>
                        </div>

                        <div class="dad-benefit">
                            <h4>📱 Screen-Free Bonding Time</h4>
                            <p>These <?php echo esc_html($city_name); ?> petting zoos create natural opportunities for conversation
                            and shared experiences without the distraction of devices.</p>
                        </div>

                        <div class="dad-benefit">
                            <h4>🎯 Easy Weekend Planning</h4>
                            <p>Most petting zoos in <?php echo esc_html($location_display); ?> are open year-round with flexible
                            hours, making them perfect for spontaneous family outings or planned weekend adventures.</p>
                        </div>

                        <div class="dad-benefit">
                            <h4>💪 Teaching Moments</h4>
                            <p>Dads love the natural teaching opportunities - from animal care and responsibility to overcoming
                            fears and building confidence in their children.</p>
                        </div>
                    </div>

                    <div class="dad-testimonial">
                        <blockquote>
                            <p>"The petting zoos in <?php echo esc_html($city_name); ?> have become our go-to father-child activity.
                            My kids learn so much, and I love seeing their faces light up when they feed the goats!"</p>
                            <cite>- Local Dad Review</cite>
                        </blockquote>
                    </div>
                </section>

                <!-- H2: Frequently Asked Questions – Petting Zoos in [City] -->
                <section class="city-faq-section faq-section">
                    <h2 class="section-title">Frequently Asked Questions – Petting Zoos in <?php echo esc_html($city_name); ?></h2>

                    <div class="faq-item">
                        <div class="faq-question">Are there petting zoos near downtown <?php echo esc_html($city_name); ?>?</div>
                        <div class="faq-answer">
                            <p>Yes! We've found <?php echo $zoo_query->found_posts; ?> petting zoo<?php echo $zoo_query->found_posts !== 1 ? 's' : ''; ?>
                            in the <?php echo esc_html($location_display); ?> area. Use our location filters above to find the ones
                            closest to your preferred area of the city.</p>
                        </div>
                    </div>

                    <div class="faq-item">
                        <div class="faq-question">Do any <?php echo esc_html($city_name); ?> petting zoos allow animal feeding?</div>
                        <div class="faq-answer">
                            <p>Many petting zoos in <?php echo esc_html($location_display); ?> offer animal feeding experiences!
                            Look for locations with "Animal Feeding" in their features list, or contact individual zoos to ask
                            about feeding schedules and opportunities.</p>
                        </div>
                    </div>

                    <div class="faq-item">
                        <div class="faq-question">What's the best age for children to visit petting zoos in <?php echo esc_html($city_name); ?>?</div>
                        <div class="faq-answer">
                            <p>Petting zoos in <?php echo esc_html($city_name); ?> welcome children of all ages! Most locations are
                            designed to be safe for toddlers (18 months+) while still engaging for older children and teens.</p>
                        </div>
                    </div>

                    <div class="faq-item">
                        <div class="faq-question">Is parking available at <?php echo esc_html($city_name); ?> petting zoos?</div>
                        <div class="faq-answer">
                            <p>Most petting zoos in <?php echo esc_html($location_display); ?> offer free on-site parking.
                            Check individual zoo pages for specific parking information and any special instructions for busy days.</p>
                        </div>
                    </div>

                    <div class="faq-item">
                        <div class="faq-question">Can we host birthday parties at petting zoos in <?php echo esc_html($city_name); ?>?</div>
                        <div class="faq-answer">
                            <p>Many locations offer birthday party packages! Look for zoos with "Birthday Parties" listed in their
                            event types, or contact them directly to discuss party options and pricing.</p>
                        </div>
                    </div>

                    <div class="faq-item">
                        <div class="faq-question">What should we expect to pay for petting zoo admission in <?php echo esc_html($city_name); ?>?</div>
                        <div class="faq-answer">
                            <p>Admission prices vary by location, but most petting zoos in <?php echo esc_html($location_display); ?>
                            offer affordable family pricing. Check individual zoo pages for current admission rates and any available discounts.</p>
                        </div>
                    </div>
                </section>

                <?php if ($is_city_page && $parent_term) : ?>
                    <!-- H2: More Cities in [State] -->
                    <section class="more-cities-section">
                        <h2>More Cities in <?php echo esc_html($state_name); ?></h2>
                        <p>Explore petting zoos in other cities throughout <?php echo esc_html($state_name); ?>. Each city offers unique experiences and family-friendly animal encounters.</p>

                        <?php
                        // Get other cities in the same state directly from taxonomy
                        $other_cities = get_terms(array(
                            'taxonomy' => 'location',
                            'hide_empty' => true,
                            'parent' => $parent_term->term_id,
                            'exclude' => array($current_term->term_id), // Exclude current city
                            'orderby' => 'count',
                            'order' => 'DESC'
                        ));

                        if (!empty($other_cities)) : ?>
                            <div class="cities-grid">
                                <?php foreach ($other_cities as $city) : ?>
                                    <a href="<?php echo get_term_link($city); ?>" class="city-card">
                                        <h4><?php echo esc_html($city->name); ?></h4>
                                        <div class="zoo-count"><?php echo $city->count; ?> Petting Zoo<?php echo $city->count !== 1 ? 's' : ''; ?></div>
                                    </a>
                                <?php endforeach; ?>
                            </div>
                        <?php else : ?>
                            <p>Currently, <?php echo esc_html($city_name); ?> is the only city in <?php echo esc_html($state_name); ?> with petting zoos in our directory. Check back soon for more locations!</p>
                        <?php endif; ?>
                    </section>
                <?php endif; ?>

        </div> <!-- End main container -->


        <div class="container" style="max-width: 1400px; margin: 0 auto; padding: 0 2rem;">
            <?php endif; ?>

        </div>
    </main>
</div>

<!-- Enhanced JavaScript with Analytics -->
<script>
function applyFilters() {
    const params = new URLSearchParams();

    document.querySelectorAll('.zoo-filters select').forEach(select => {
        if (select.value) {
            params.set(select.name, select.value);
        }
    });

    const currentUrl = window.location.pathname;
    const queryString = params.toString();

    if (queryString) {
        window.location.href = currentUrl + '?' + queryString;
    } else {
        window.location.href = currentUrl;
    }
}

// Track filter usage for analytics
document.addEventListener('DOMContentLoaded', function() {
    document.querySelectorAll('.zoo-filters select').forEach(select => {
        select.addEventListener('change', function() {
            // Track filter usage if analytics is available
            if (typeof gtag !== 'undefined') {
                gtag('event', 'filter_used', {
                    'filter_type': this.name,
                    'filter_value': this.value,
                    'page_location': window.location.pathname
                });
            }
        });
    });
});
</script>

<?php
// Add structured data for city pages
if ($is_city_page && $zoo_query->have_posts()) :
    $structured_data = array(
        '@context' => 'https://schema.org',
        '@type' => 'FAQPage',
        'mainEntity' => array(
            array(
                '@type' => 'Question',
                'name' => 'Are there petting zoos near downtown ' . $city_name . '?',
                'acceptedAnswer' => array(
                    '@type' => 'Answer',
                    'text' => 'Yes! We\'ve found ' . $zoo_query->found_posts . ' petting zoo' . ($zoo_query->found_posts !== 1 ? 's' : '') . ' in the ' . $location_display . ' area.'
                )
            ),
            array(
                '@type' => 'Question',
                'name' => 'Do any ' . $city_name . ' petting zoos allow animal feeding?',
                'acceptedAnswer' => array(
                    '@type' => 'Answer',
                    'text' => 'Many petting zoos in ' . $location_display . ' offer animal feeding experiences! Look for locations with "Animal Feeding" in their features list.'
                )
            ),
            array(
                '@type' => 'Question',
                'name' => 'What\'s the best age for children to visit petting zoos in ' . $city_name . '?',
                'acceptedAnswer' => array(
                    '@type' => 'Answer',
                    'text' => 'Petting zoos in ' . $city_name . ' welcome children of all ages! Most locations are designed to be safe for toddlers (18 months+) while still engaging for older children and teens.'
                )
            )
        )
    );
    ?>
    <script type="application/ld+json">
    <?php echo wp_json_encode($structured_data, JSON_UNESCAPED_SLASHES | JSON_PRETTY_PRINT); ?>
    </script>
<?php endif; ?>

<?php get_footer(); ?>
